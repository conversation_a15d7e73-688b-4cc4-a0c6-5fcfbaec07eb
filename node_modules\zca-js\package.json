{"name": "zca-js", "version": "2.0.0-beta.24", "description": "Unofficial Zalo API for JavaScript", "main": "dist/index.js", "type": "module", "exports": {".": {"require": "./dist/cjs/index.cjs", "types": "./index.d.ts", "default": "./dist/index.js"}}, "scripts": {"build:clean": "<PERSON><PERSON><PERSON> dist", "build:esm": "tsc", "build:cjs": "rollup -c rollup.config.js", "build": "bun run build:clean && bun run build:esm && bun run build:cjs", "test:feat": "bun run test/feat.ts", "prettier": "prettier --write ."}, "repository": {"type": "git", "url": "git+https://github.com/RFS-ADRENO/zca-js.git"}, "homepage": "https://github.com/RFS-ADRENO/zca-js", "bugs": {"url": "https://github.com/RFS-ADRENO/zca-js/issues"}, "engines": {"node": ">=18.0.0"}, "keywords": ["chatbot", "zalo", "api"], "author": "RFS-ADRENO, truong9c2208", "license": "MIT", "dependencies": {"crypto-js": "^4.2.0", "form-data": "^4.0.0", "pako": "^2.1.0", "semver": "^7.6.3", "sharp": "^0.33.4", "spark-md5": "^3.0.2", "tough-cookie": "^5.0.0", "ws": "^8.18.0"}, "devDependencies": {"@rollup/plugin-typescript": "^12.1.0", "@types/bun": "^1.1.14", "@types/crypto-js": "^4.2.2", "@types/node": "^20.14.10", "@types/pako": "^2.0.3", "@types/semver": "^7.5.8", "@types/spark-md5": "^3.0.4", "@types/ws": "^8.5.11", "prettier": "^3.3.3", "rimraf": "^5.0.10", "rollup": "^4.24.0", "typescript": "^5.5.3"}}
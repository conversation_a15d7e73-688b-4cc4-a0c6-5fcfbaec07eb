<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Danh sách <PERSON></title>
    <style>
      body {
        font-family: Arial, sans-serif;
        padding: 20px;
      }
      .entity-list {
        margin-top: 20px;
      }
      .entity-item {
        padding: 10px;
        border-bottom: 1px solid #ccc;
        display: flex;
        align-items: center;
      }
      .entity-item img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-right: 10px;
      }
      .status-message {
        padding: 10px;
        margin-bottom: 10px;
      }
      .status-message.success {
        background-color: #dff0d8;
        color: #3c763d;
      }
      .status-message.error {
        background-color: #f2dede;
        color: #a94442;
      }
      .status-message.info {
        background-color: #d9edf7;
        color: #31708f;
      }
      .filter-container {
        margin-bottom: 10px;
      }
      .filter-container input {
        padding: 5px;
        width: 200px;
      }
      .template-container {
        margin-bottom: 10px;
      }
      .template-container select {
        padding: 5px;
        width: 200px;
      }
      .action-container {
        margin-bottom: 10px;
      }
      .action-container button {
        padding: 5px 10px;
        margin-right: 10px;
      }
    </style>
  </head>
  <body>
    <h1>Danh sách Chats</h1>
    <a href="/templates">Quản lý Mẫu Tin nhắn</a>
    <div class="filter-container">
      <input type="text" id="filter-input" placeholder="Tìm kiếm theo tên..." />
    </div>
    <div class="template-container">
      <select id="template-select">
        <option value="">Chọn mẫu tin nhắn</option>
      </select>
    </div>
    <div class="action-container">
      <button id="send-template-btn" disabled>Gửi Template</button>
      <button id="refresh-btn">Làm mới danh sách</button>
      <button id="logout-btn">Đăng xuất</button>
    </div>
    <div id="status-message" class="status-message" style="display: none"></div>
    <div id="entity-list" class="entity-list"></div>

    <script src="/socket.io/socket.io.js"></script>
    <script>
      const socket = io();
      const refreshBtn = document.getElementById("refresh-btn");
      const logoutBtn = document.getElementById("logout-btn");
      const sendTemplateBtn = document.getElementById("send-template-btn");
      const statusMessage = document.getElementById("status-message");
      const entityList = document.getElementById("entity-list");
      const filterInput = document.getElementById("filter-input");
      const templateSelect = document.getElementById("template-select");
      let entities = [];

      socket.on("connect", () => {
        console.log("Connected to server");
      });

      socket.on("chats", ({ chats }) => {
        entities = chats;
        renderEntities(entities);
        showStatus(
          `Đã tải ${entities.length} chats (groups và friends)`,
          "success"
        );
      });

      socket.on("templates", ({ templates }) => {
        templateSelect.innerHTML =
          '<option value="">Chọn mẫu tin nhắn</option>';
        templates.forEach((template) => {
          const option = document.createElement("option");
          option.value = template.id;
          option.textContent = template.displayName;
          templateSelect.appendChild(option);
        });
      });

      socket.on("status", ({ status, message, redirect }) => {
        showStatus(message, status);
        if (redirect) {
          setTimeout(() => {
            window.location.href = redirect;
          }, 1000);
        }
      });

      socket.on("error", ({ message }) => {
        showStatus(message, "error");
      });

      refreshBtn.addEventListener("click", () => {
        socket.emit("refresh_chats");
        showStatus("Đang làm mới danh sách...", "info");
      });

      logoutBtn.addEventListener("click", async () => {
        try {
          const response = await fetch("/api/logout", { method: "POST" });
          const data = await response.json();
          if (data.success) {
            showStatus("Đăng xuất thành công", "success");
            setTimeout(() => {
              window.location.href = "/";
            }, 1000);
          } else {
            showStatus("Lỗi đăng xuất: " + data.message, "error");
          }
        } catch (error) {
          showStatus("Lỗi đăng xuất: " + error.message, "error");
        }
      });

      templateSelect.addEventListener("change", () => {
        sendTemplateBtn.disabled = !templateSelect.value;
      });

      sendTemplateBtn.addEventListener("click", async () => {
        const selectedChatIds = Array.from(
          document.querySelectorAll(".chat-checkbox:checked")
        ).map((checkbox) => checkbox.value);
        const templateId = templateSelect.value;

        if (!templateId) {
          showStatus("Vui lòng chọn một mẫu tin nhắn", "error");
          return;
        }
        if (selectedChatIds.length === 0) {
          showStatus("Vui lòng chọn ít nhất một đoạn chat", "error");
          return;
        }

        try {
          const response = await fetch("/api/send-message", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ chatIds: selectedChatIds, templateId }),
          });
          const data = await response.json();
          if (data.success) {
            showStatus("Gửi tin nhắn thành công", "success");
          } else {
            showStatus(data.message, "error");
          }
        } catch (error) {
          showStatus(`Lỗi khi gửi tin nhắn: ${error.message}`, "error");
        }
      });

      filterInput.addEventListener("input", () => {
        const query = filterInput.value.toLowerCase();
        const filteredEntities = entities.filter((entity) =>
          entity.displayName.toLowerCase().includes(query)
        );
        renderEntities(filteredEntities);
      });

      function renderEntities(entities) {
        entityList.innerHTML = "";
        entities.forEach((entity) => {
          const entityItem = document.createElement("div");
          entityItem.className = "entity-item";
          entityItem.innerHTML = `
          <input type="checkbox" class="chat-checkbox" value="${entity.id}">
          <img src="${entity.avatar}" alt="${entity.displayName}'s avatar">
          <div>
            <strong>${entity.displayName}</strong> (${
            entity.type === "group" ? "Nhóm" : "Bạn bè"
          })<br>
            ID: ${entity.id}
          </div>
        `;
          entityList.appendChild(entityItem);
        });
      }

      function showStatus(message, type = "info") {
        statusMessage.textContent = message;
        statusMessage.className = `status-message ${type}`;
        statusMessage.style.display = "block";
      }
    </script>
  </body>
</html>

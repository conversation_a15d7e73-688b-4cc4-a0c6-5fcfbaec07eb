<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON><PERSON><PERSON><PERSON> lý Mẫu <PERSON>n</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        padding: 20px;
      }
      .template-container {
        margin-top: 20px;
      }
      .template-item {
        padding: 10px;
        border-bottom: 1px solid #ccc;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .status-message {
        padding: 10px;
        margin-bottom: 10px;
      }
      .status-message.success {
        background-color: #dff0d8;
        color: #3c763d;
      }
      .status-message.error {
        background-color: #f2dede;
        color: #a94442;
      }
      .status-message.info {
        background-color: #d9edf7;
        color: #31708f;
      }
      .form-container {
        margin-bottom: 20px;
      }
      .form-container input {
        margin: 5px;
        padding: 5px;
        width: 200px;
      }
      .form-container button {
        padding: 5px 10px;
      }
    </style>
  </head>
  <body>
    <h1><PERSON><PERSON><PERSON><PERSON> lý Mẫu <PERSON></h1>
    <a href="/chats"><PERSON>uay lạ<PERSON></a>
    <div id="status-message" class="status-message" style="display: none"></div>
    <div class="form-container">
      <input id="template-id" type="hidden" />
      <input id="template-name" placeholder="Tên mẫu tin nhắn" />
      <input id="template-content" placeholder="Nội dung mẫu tin nhắn" />
      <button id="save-btn">Lưu</button>
      <button id="clear-btn">Xóa form</button>
    </div>
    <div id="template-list" class="template-container"></div>

    <script src="/socket.io/socket.io.js"></script>
    <script>
      const socket = io();
      const statusMessage = document.getElementById("status-message");
      const templateList = document.getElementById("template-list");
      const templateIdInput = document.getElementById("template-id");
      const templateNameInput = document.getElementById("template-name");
      const templateContentInput = document.getElementById("template-content");
      const saveBtn = document.getElementById("save-btn");
      const clearBtn = document.getElementById("clear-btn");

      socket.on("connect", () => {
        console.log("Connected to server");
        fetchTemplates();
      });

      socket.on("status", ({ status, message, redirect }) => {
        showStatus(message, status);
        if (redirect) {
          setTimeout(() => {
            window.location.href = redirect;
          }, 1000);
        }
      });

      socket.on("templates", ({ templates }) => {
        renderTemplates(templates);
      });

      saveBtn.addEventListener("click", async () => {
        const id = templateIdInput.value;
        const displayName = templateNameInput.value.trim();
        const content = templateContentInput.value.trim();
        if (!displayName || !content) {
          showStatus("Vui lòng nhập tên và nội dung mẫu tin nhắn", "error");
          return;
        }
        try {
          const url = id ? `/api/templates/${id}` : "/api/templates";
          const method = id ? "PUT" : "POST";
          const response = await fetch(url, {
            method,
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ displayName, content }),
          });
          const data = await response.json();
          if (data.success) {
            showStatus(
              id
                ? "Cập nhật mẫu tin nhắn thành công"
                : "Tạo mẫu tin nhắn thành công",
              "success"
            );
            clearForm();
          } else {
            showStatus(data.message, "error");
          }
        } catch (error) {
          showStatus(`Lỗi: ${error.message}`, "error");
        }
      });

      clearBtn.addEventListener("click", () => {
        clearForm();
        showStatus("Đã xóa form", "info");
      });

      function renderTemplates(templates) {
        templateList.innerHTML = "";
        templates.forEach((template) => {
          const templateItem = document.createElement("div");
          templateItem.className = "template-item";
          templateItem.innerHTML = `
          <div>
            <strong>${template.displayName}</strong><br>
            Nội dung: ${template.content}<br>
            ID: ${template.id}
          </div>
          <div>
            <button onclick="editTemplate('${template.id}', '${
            template.displayName
          }', '${template.content.replace(/'/g, "\\'")}')">Sửa</button>
            <button onclick="deleteTemplate('${template.id}')">Xóa</button>
          </div>
        `;
          templateList.appendChild(templateItem);
        });
      }

      async function fetchTemplates() {
        try {
          const response = await fetch("/api/templates");
          const data = await response.json();
          if (data.success) {
            renderTemplates(data.templates);
          } else {
            showStatus(data.message, "error");
          }
        } catch (error) {
          showStatus(`Lỗi khi tải danh sách mẫu: ${error.message}`, "error");
        }
      }

      function editTemplate(id, displayName, content) {
        templateIdInput.value = id;
        templateNameInput.value = displayName;
        templateContentInput.value = content;
        showStatus("Đang chỉnh sửa mẫu tin nhắn", "info");
      }

      async function deleteTemplate(id) {
        if (!confirm("Bạn có chắc muốn xóa mẫu tin nhắn này?")) return;
        try {
          const response = await fetch(`/api/templates/${id}`, {
            method: "DELETE",
          });
          const data = await response.json();
          if (data.success) {
            showStatus("Xóa mẫu tin nhắn thành công", "success");
          } else {
            showStatus(data.message, "error");
          }
        } catch (error) {
          showStatus(`Lỗi khi xóa mẫu: ${error.message}`, "error");
        }
      }

      function clearForm() {
        templateIdInput.value = "";
        templateNameInput.value = "";
        templateContentInput.value = "";
      }

      function showStatus(message, type = "info") {
        statusMessage.textContent = message;
        statusMessage.className = `status-message ${type}`;
        statusMessage.style.display = "block";
      }
    </script>
  </body>
</html>

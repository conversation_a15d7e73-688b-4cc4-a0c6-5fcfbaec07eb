import express from 'express';
import { createServer } from 'http';
import { Server } from 'socket.io';
import path from 'path';
import { fileURLToPath } from 'url';
import cors from 'cors';
import fs from 'fs/promises';
import { <PERSON>alo } from 'zca-js';
import { getAllGroupsFactory } from './getAllGroups.ts';
import { getGroupInfoFactory } from './getGroupInfo.ts';
import { ChatEntityFactory } from './ChatEntity.ts';
import { MessageTemplateManager } from './MessageTemplate.ts';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const PORT = process.env.PORT || 3000;
const CORS_ORIGINS = ['http://localhost:3000']; // Restrict in production
const FILE_PATHS = {
  authFile: 'auth.json',
  qrFile: 'qr.png',
  publicDir: 'public',
  templateFile: 'templates.json'
};
const API_ENDPOINTS = {
  status: '/api/status',
  qr: '/api/qr',
  chats: '/api/chats',
  logout: '/api/logout',
  qrImage: '/qr.png',
  templates: '/api/templates',
  sendMessage: '/api/send-message'
};

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: CORS_ORIGINS,
    methods: ['GET', 'POST']
  }
});

// Middleware
app.use(cors({ origin: CORS_ORIGINS }));
app.use(express.json());
app.use(express.static(path.join(__dirname, FILE_PATHS.publicDir)));

// Session store (simplified for single user; use Redis or DB for multi-user)
const session = {
  zaloApi: null,
  loginStatus: 'waiting',
  chatList: []
};

// Initialize template manager
const templateManager = new MessageTemplateManager(FILE_PATHS.templateFile);

// File utilities
const fileUtils = {
  async checkAuth() {
    try {
      const authData = await fs.readFile(path.join(__dirname, FILE_PATHS.authFile), 'utf8');
      const { status, timestamp } = JSON.parse(authData);
      const sessionAge = Date.now() - timestamp;
      const maxAge = 24 * 60 * 60 * 1000; // 24 hours
      if (status === 'success' && sessionAge < maxAge) {
        console.log('Valid session found in auth.json');
        return true;
      }
      await fileUtils.clearAuth();
      return false;
    } catch (error) {
      console.warn('Error checking auth.json:', error.message);
      return false;
    }
  },
  async clearAuth() {
    try {
      await fs.unlink(path.join(__dirname, FILE_PATHS.authFile));
      console.log('Cleared auth.json');
    } catch (error) {
      if (error.code !== 'ENOENT') console.warn('Error clearing auth.json:', error.message);
    }
  },
  async clearQR() {
    try {
      await fs.unlink(path.join(__dirname, FILE_PATHS.qrFile));
      console.log('Cleared QR file');
    } catch (error) {
      if (error.code !== 'ENOENT') console.warn('Error clearing QR file:', error.message);
    }
  },
  async saveAuth() {
    try {
      const authData = {
        loginTime: new Date().toISOString(),
        status: 'success',
        timestamp: Date.now()
      };
      await fs.writeFile(path.join(__dirname, FILE_PATHS.authFile), JSON.stringify(authData, null, 2));
      console.log('Saved auth.json');
    } catch (error) {
      console.warn('Error saving auth.json:', error.message);
    }
  }
};

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Server error:', err);
  res.status(500).json({ success: false, message: `Server error: ${err.message}` });
});

// Function to send a message to a single chat using Zalo API
async function sendZaloMessage(zaloApi, chatId, content) {
  try {
    await zaloApi.sendMessage(chatId, content);
    return { success: true };
  } catch (error) {
    console.error(`Error sending message to chat ${chatId}:`, error);
    return { success: false, message: error.message };
  }
}

// Function to create QR code
async function createQRCode() {
  try {
    session.loginStatus = 'waiting';
    session.chatList = [];
    session.zaloApi = null;
    await fileUtils.clearQR();

    io.emit('status', { status: 'generating_qr', message: 'Đang tạo mã QR...' });
    const zalo = new Zalo();
    session.zaloApi = await zalo.loginQR();

    io.emit('status', { status: 'waiting', message: 'Quét mã QR để đăng nhập' });
    session.zaloApi.listener.start();

    session.zaloApi.listener.onConnected(async () => {
      session.loginStatus = 'success';
      await fileUtils.saveAuth();
      io.emit('status', { status: 'success', message: 'Đăng nhập thành công! Đang chuyển hướng...', redirect: '/chats' });

      try {
        // Fetch friends
        const friends = (await session.zaloApi.getAllFriends()) || [];
        const friendEntities = friends.map(friend => ChatEntityFactory.createFromFriend(friend));

        // Fetch groups
        const getAllGroups = getAllGroupsFactory(session.zaloApi);
        const groupsResponse = await getAllGroups();
        const groupIds = Object.keys(groupsResponse.gridInfoMap);
        const getGroupInfo = getGroupInfoFactory(session.zaloApi);
        const groupInfoResponse = await getGroupInfo(groupIds);
        const groupEntities = Object.values(groupInfoResponse.gridInfoMap).map(group =>
          ChatEntityFactory.createFromGroup(group)
        );

        // Combine friends and groups into chatList
        session.chatList = [...friendEntities, ...groupEntities];
        io.emit('chats', { chats: session.chatList });
        // Emit templates after login
        const templates = await templateManager.getAllTemplates();
        io.emit('templates', { templates });
        console.log(`Loaded ${session.chatList.length} chats (friends and groups)`);
      } catch (error) {
        console.error('Error loading chats:', error);
        session.chatList = [];
        io.emit('status', { status: 'error', message: `Lỗi khi tải danh sách chat: ${error.message}` });
      }
    });

    session.zaloApi.listener.on('error', (error) => {
      console.error('Listener error:', error);
      session.loginStatus = 'error';
      io.emit('status', { status: 'error', message: `Lỗi đăng nhập: ${error.message}` });
    });

    return { success: true, message: 'Đã tạo mã QR thành công' };
  } catch (error) {
    console.error('Error creating QR:', error);
    session.loginStatus = 'error';
    io.emit('status', { status: 'error', message: `Lỗi khi tạo QR: ${error.message}` });
    return { success: false, message: `Lỗi khi tạo QR: ${error.message}` };
  }
}

// Routes
app.get('/', async (req, res) => {
  if (await fileUtils.checkAuth() && session.loginStatus === 'success') {
    res.redirect('/chats');
  } else {
    const qrResult = await createQRCode();
    if (!qrResult.success) {
      io.emit('status', { status: 'error', message: qrResult.message });
    }
    res.sendFile(path.join(__dirname, FILE_PATHS.publicDir, 'login.html'));
  }
});

app.get('/chats', async (req, res) => {
  if (!(await fileUtils.checkAuth()) || session.loginStatus !== 'success') {
    res.redirect('/');
  } else {
    res.sendFile(path.join(__dirname, FILE_PATHS.publicDir, 'chats.html'));
  }
});

app.get('/templates', (req, res) => {
  res.sendFile(path.join(__dirname, FILE_PATHS.publicDir, 'templates.html'));
});

app.get(API_ENDPOINTS.qrImage, async (req, res) => {
  const qrPath = path.join(__dirname, FILE_PATHS.qrFile);
  try {
    await fs.access(qrPath);
    res.sendFile(qrPath);
  } catch {
    res.status(404).send('QR code not found');
  }
});

// API endpoints
app.get(API_ENDPOINTS.status, async (req, res) => {
  res.json({
    status: session.loginStatus,
    hasQR: await fs.access(path.join(__dirname, FILE_PATHS.qrFile)).then(() => true).catch(() => false)
  });
});

app.get(API_ENDPOINTS.chats, (req, res) => {
  res.json({ chats: session.chatList });
});

app.post(API_ENDPOINTS.logout, async (req, res) => {
  session.loginStatus = 'waiting';
  session.chatList = [];
  session.zaloApi = null;
  await fileUtils.clearAuth();
  await fileUtils.clearQR();
  io.emit('status', { status: 'logged_out', message: 'Đã đăng xuất' });
  res.json({ success: true, message: 'Đăng xuất thành công' });
});

app.post(API_ENDPOINTS.qr, async (req, res) => {
  const { action } = req.body; // 'create' or 'refresh'
  if (session.loginStatus === 'success') {
    return res.status(400).json({ success: false, message: 'Đã đăng nhập rồi' });
  }

  const qrResult = await createQRCode();
  res.status(qrResult.success ? 200 : 500).json(qrResult);
});

// Template API endpoints
app.get(API_ENDPOINTS.templates, async (req, res) => {
  try {
    const templates = await templateManager.getAllTemplates();
    res.json({ success: true, templates });
  } catch (error) {
    res.status(500).json({ success: false, message: `Error fetching templates: ${error.message}` });
  }
});

app.post(API_ENDPOINTS.templates, async (req, res) => {
  const { displayName, content } = req.body;
  if (!displayName || !content) {
    return res.status(400).json({ success: false, message: 'Missing displayName or content' });
  }
  try {
    const newTemplate = await templateManager.createTemplate(displayName, content);
    const templates = await templateManager.getAllTemplates();
    io.emit('templates', { templates });
    res.json({ success: true, template: newTemplate });
  } catch (error) {
    res.status(500).json({ success: false, message: `Error creating template: ${error.message}` });
  }
});

app.put(`${API_ENDPOINTS.templates}/:id`, async (req, res) => {
  const { id } = req.params;
  const { displayName, content } = req.body;
  if (!displayName || !content) {
    return res.status(400).json({ success: false, message: 'Missing displayName or content' });
  }
  try {
    const updatedTemplate = await templateManager.updateTemplate(id, displayName, content);
    if (!updatedTemplate) {
      return res.status(404).json({ success: false, message: 'Template not found' });
    }
    const templates = await templateManager.getAllTemplates();
    io.emit('templates', { templates });
    res.json({ success: true, template: updatedTemplate });
  } catch (error) {
    res.status(500).json({ success: false, message: `Error updating template: ${error.message}` });
  }
});

app.delete(`${API_ENDPOINTS.templates}/:id`, async (req, res) => {
  const { id } = req.params;
  try {
    const deleted = await templateManager.deleteTemplate(id);
    if (!deleted) {
      return res.status(404).json({ success: false, message: 'Template not found' });
    }
    const templates = await templateManager.getAllTemplates();
    io.emit('templates', { templates });
    res.json({ success: true, message: 'Template deleted' });
  } catch (error) {
    res.status(500).json({ success: false, message: `Error deleting template: ${error.message}` });
  }
});

app.post(API_ENDPOINTS.sendMessage, async (req, res) => {
  if (!(await fileUtils.checkAuth()) || session.loginStatus !== 'success') {
    return res.status(401).json({ success: false, message: 'Chưa đăng nhập' });
  }
  if (!session.zaloApi) {
    return res.status(500).json({ success: false, message: 'Zalo API không khả dụng' });
  }

  const { chatIds, templateId } = req.body;
  if (!Array.isArray(chatIds) || chatIds.length === 0 || !templateId) {
    return res.status(400).json({ success: false, message: 'Thiếu chatIds hoặc templateId' });
  }

  try {
    const template = await templateManager.getTemplateById(templateId);
    if (!template) {
      return res.status(404).json({ success: false, message: 'Không tìm thấy mẫu tin nhắn' });
    }

    const results = [];
    for (const chatId of chatIds) {
      const result = await sendZaloMessage(session.zaloApi, chatId, template.content);
      results.push({ chatId, ...result });
    }

    const failed = results.filter(r => !r.success);
    if (failed.length > 0) {
      return res.status(500).json({
        success: false,
        message: `Gửi tin nhắn thất bại cho ${failed.length} đoạn chat`,
        results
      });
    }

    res.json({ success: true, message: 'Gửi tin nhắn thành công', results });
  } catch (error) {
    console.error('Error processing send message request:', error);
    res.status(500).json({ success: false, message: `Lỗi khi gửi tin nhắn: ${error.message}` });
  }
});

// Socket.IO
io.on('connection', (socket) => {
  console.log('Client connected:', socket.id);
  socket.emit('status', {
    status: session.loginStatus,
    hasQR: fs.access(path.join(__dirname, FILE_PATHS.qrFile)).then(() => true).catch(() => false)
  });

  if (session.loginStatus === 'success' && session.chatList.length > 0) {
    socket.emit('chats', { chats: session.chatList });
  }
  templateManager.getAllTemplates().then(templates => {
    socket.emit('templates', { templates });
  }).catch(error => {
    console.error('Error emitting templates on connect:', error);
  });

  socket.on('disconnect', () => console.log('Client disconnected:', socket.id));

  socket.on('refresh_chats', async () => {
    if (session.zaloApi && session.loginStatus === 'success') {
      try {
        // Fetch friends
        const friends = (await session.zaloApi.getAllFriends()) || [];
        const friendEntities = friends.map(friend => ChatEntityFactory.createFromFriend(friend));

        // Fetch groups
        const getAllGroups = getAllGroupsFactory(session.zaloApi);
        const groupsResponse = await getAllGroups();
        const groupIds = Object.keys(groupsResponse.gridInfoMap);
        const getGroupInfo = getGroupInfoFactory(session.zaloApi);
        const groupInfoResponse = await getGroupInfo(groupIds);
        const groupEntities = Object.values(groupInfoResponse.gridInfoMap).map(group =>
          ChatEntityFactory.createFromGroup(group)
        );

        // Combine friends and groups into chatList
        session.chatList = [...friendEntities, ...groupEntities];
        io.emit('chats', { chats: session.chatList });
        // Emit templates on refresh
        const templates = await templateManager.getAllTemplates();
        io.emit('templates', { templates });
        console.log(`Refreshed ${session.chatList.length} chats (friends and groups)`);
      } catch (error) {
        console.error('Error refreshing chats:', error);
        socket.emit('status', { status: 'error', message: 'Không thể tải lại danh sách chat' });
      }
    }
  });
});

server.listen(PORT, () => {
  console.log(`Server running at http://localhost:${PORT}`);
});
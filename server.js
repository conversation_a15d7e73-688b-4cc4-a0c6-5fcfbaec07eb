import express from 'express';
import { createServer } from 'http';
import { Server } from 'socket.io';
import path from 'path';
import { fileURLToPath } from 'url';
import cors from 'cors';
import fs from 'fs';
import { <PERSON>alo } from 'zca-js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, 'public')));

// Biến global để lưu trạng thái
let zaloApi = null;
let loginStatus = 'waiting'; // 'waiting', 'success', 'error'
let chatList = [];

// Hàm kiểm tra trạng thái đăng nhập từ file auth.json
function checkAuthStatus() {
  try {
    const authPath = path.join(__dirname, 'auth.json');
    if (fs.existsSync(authPath)) {
      const authData = JSON.parse(fs.readFileSync(authPath, 'utf8'));

      // Kiểm tra xem session có còn hợp lệ không (ví dụ: trong vòng 24 giờ)
      const now = Date.now();
      const sessionAge = now - authData.timestamp;
      const maxAge = 24 * 60 * 60 * 1000; // 24 giờ

      if (authData.status === 'success' && sessionAge < maxAge) {
        console.log("Tìm thấy session đăng nhập hợp lệ từ auth.json");
        return true;
      } else {
        console.log("Session đăng nhập đã hết hạn, xóa auth.json");
        fs.unlinkSync(authPath);
      }
    }
  } catch (error) {
    console.warn("Lỗi khi kiểm tra auth.json:", error.message);
  }
  return false;
}

// Hàm xóa thông tin đăng nhập
function clearAuth() {
  try {
    const authPath = path.join(__dirname, 'auth.json');
    if (fs.existsSync(authPath)) {
      fs.unlinkSync(authPath);
      console.log("Đã xóa thông tin đăng nhập");
    }
  } catch (error) {
    console.warn("Lỗi khi xóa auth.json:", error.message);
  }
}

// Routes
app.get('/', (req, res) => {
  // Nếu đã đăng nhập, chuyển hướng đến trang chats
  if (checkAuthStatus() && loginStatus === 'success') {
    res.redirect('/chats');
  } else {
    res.sendFile(path.join(__dirname, 'public', 'login.html'));
  }
});

app.get('/chats', (req, res) => {
  // Kiểm tra xem có đăng nhập không
  if (!checkAuthStatus() || loginStatus !== 'success') {
    res.redirect('/');
  } else {
    res.sendFile(path.join(__dirname, 'public', 'chats.html'));
  }
});

app.get('/qr.png', (req, res) => {
  const qrPath = path.join(__dirname, 'qr.png');
  if (fs.existsSync(qrPath)) {
    res.sendFile(qrPath);
  } else {
    res.status(404).send('QR code not found');
  }
});

// API endpoints
app.get('/api/status', (req, res) => {
  res.json({ 
    status: loginStatus,
    hasQR: fs.existsSync(path.join(__dirname, 'qr.png'))
  });
});

app.get('/api/chats', (req, res) => {
  res.json({ chats: chatList });
});

app.post('/api/logout', (req, res) => {
  try {
    // Reset trạng thái
    loginStatus = 'waiting';
    chatList = [];
    zaloApi = null;

    // Xóa file auth.json
    clearAuth();

    // Xóa file QR nếu có
    const qrPath = path.join(__dirname, 'qr.png');
    if (fs.existsSync(qrPath)) {
      fs.unlinkSync(qrPath);
    }

    console.log("Đã đăng xuất thành công");
    io.emit('status', { status: 'logged_out', message: 'Đã đăng xuất' });

    res.json({ success: true, message: 'Đăng xuất thành công' });
  } catch (error) {
    console.error("Lỗi khi đăng xuất:", error);
    res.status(500).json({ success: false, message: 'Lỗi khi đăng xuất: ' + error.message });
  }
});

app.post('/api/refresh-qr', async (req, res) => {
  try {
    if (loginStatus === 'success') {
      return res.status(400).json({ success: false, message: 'Đã đăng nhập rồi' });
    }

    // Xóa file QR cũ
    const qrPath = path.join(__dirname, 'qr.png');
    if (fs.existsSync(qrPath)) {
      try {
        fs.unlinkSync(qrPath);
        console.log("Đã xóa file QR cũ để tạo mới");
      } catch (deleteError) {
        console.warn("Không thể xóa file QR cũ:", deleteError.message);
      }
    }

    // Reset trạng thái
    loginStatus = 'waiting';
    chatList = [];
    zaloApi = null;

    console.log("Đang tạo mã QR mới...");
    io.emit('status', { status: 'generating_qr', message: 'Đang tạo mã QR mới...' });

    // Khởi tạo Zalo instance mới và tạo QR
    const zalo = new Zalo();
    zaloApi = await zalo.loginQR();

    console.log("Đã tạo mã QR mới thành công!");
    io.emit('status', { status: 'waiting', message: 'Quét mã QR mới để đăng nhập' });

    res.json({ success: true, message: 'Đã tạo mã QR mới thành công' });

  } catch (error) {
    console.error("Lỗi khi làm mới QR:", error);
    loginStatus = 'error';
    io.emit('status', { status: 'error', message: 'Lỗi khi tạo QR mới: ' + error.message });
    res.status(500).json({ success: false, message: 'Lỗi khi làm mới QR: ' + error.message });
  }
});

app.post('/api/login', async (req, res) => {
  try {
    loginStatus = 'waiting';
    chatList = [];

    // Xóa file QR cũ nếu tồn tại
    const qrPath = path.join(__dirname, 'qr.png');
    if (fs.existsSync(qrPath)) {
      try {
        fs.unlinkSync(qrPath);
        console.log("Đã xóa file QR cũ");
      } catch (deleteError) {
        console.warn("Không thể xóa file QR cũ:", deleteError.message);
      }
    }

    // Khởi tạo Zalo instance
    const zalo = new Zalo();
    console.log("Đang tạo mã QR để đăng nhập...");
    io.emit('status', { status: 'generating_qr', message: 'Đang tạo mã QR...' });

    // Đăng nhập bằng QR
    zaloApi = await zalo.loginQR();
    zaloApi.listener.start();
    console.log("Đã khởi động listener");
    console.log("Đăng nhập thành công!");
    loginStatus = 'success';

    // Lưu thông tin đăng nhập vào file auth.json
    try {
      const authData = {
        loginTime: new Date().toISOString(),
        status: 'success',
        timestamp: Date.now()
      };

      fs.writeFileSync(path.join(__dirname, 'auth.json'), JSON.stringify(authData, null, 2));
      console.log("Đã lưu thông tin đăng nhập vào auth.json");
    } catch (authError) {
      console.warn("Không thể lưu thông tin đăng nhập:", authError.message);
    }

    // Emit success status với redirect flag
    io.emit('status', {
      status: 'success',
      message: 'Đăng nhập thành công! Đang chuyển hướng...',
      redirect: '/chats'
    });
    
    // Lấy danh sách chat
    try {
      const chats = await zaloApi.getContext();
      chatList = chats || [];

      io.emit('chats', { chats: chatList });
      console.log(`Đã tải ${chatList.length} đoạn chat`);
    } catch (chatError) {
      console.error("Lỗi khi lấy danh sách chat:", chatError);
      chatList = [];
    }
    
    res.json({ success: true, message: 'Đăng nhập thành công' });
    
  } catch (error) {
    console.error("Đã xảy ra lỗi:", error);
    loginStatus = 'error';
    io.emit('status', { status: 'error', message: 'Lỗi đăng nhập: ' + error.message });
    res.status(500).json({ success: false, message: error.message });
  }
});

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log('Client connected:', socket.id);
  
  // Gửi trạng thái hiện tại cho client mới
  socket.emit('status', { 
    status: loginStatus,
    hasQR: fs.existsSync(path.join(__dirname, 'qr.png'))
  });
  
  if (loginStatus === 'success' && chatList.length > 0) {
    socket.emit('chats', { chats: chatList });
  }
  
  socket.on('disconnect', () => {
    console.log('Client disconnected:', socket.id);
  });
  
  socket.on('refresh_chats', async () => {
    if (zaloApi && loginStatus === 'success') {
      try {
        const chats = await zaloApi.getContext();
        chatList = chats || [];
        io.emit('chats', { chats: chatList });
      } catch (error) {
        console.error("Lỗi khi refresh chat:", error);
        socket.emit('error', { message: 'Không thể tải lại danh sách chat' });
      }
    }
  });
});

const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
  console.log(`Server đang chạy tại http://localhost:${PORT}`);
});

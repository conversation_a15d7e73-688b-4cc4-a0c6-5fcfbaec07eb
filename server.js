import express from 'express';
import { createServer } from 'http';
import { Server } from 'socket.io';
import path from 'path';
import { fileURLToPath } from 'url';
import cors from 'cors';
import fs from 'fs';
import { <PERSON>alo } from 'zca-js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, 'public')));

// Biến global để lưu trạng thái
let zaloApi = null;
let loginStatus = 'waiting'; // 'waiting', 'success', 'error'
let chatList = [];

// Routes
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'login.html'));
});

app.get('/chats', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'chats.html'));
});

app.get('/qr.png', (req, res) => {
  const qrPath = path.join(__dirname, 'qr.png');
  if (fs.existsSync(qrPath)) {
    res.sendFile(qrPath);
  } else {
    res.status(404).send('QR code not found');
  }
});

// API endpoints
app.get('/api/status', (req, res) => {
  res.json({ 
    status: loginStatus,
    hasQR: fs.existsSync(path.join(__dirname, 'qr.png'))
  });
});

app.get('/api/chats', (req, res) => {
  res.json({ chats: chatList });
});

app.post('/api/refresh-qr', async (req, res) => {
  try {
    if (loginStatus === 'success') {
      return res.status(400).json({ success: false, message: 'Đã đăng nhập rồi' });
    }

    // Xóa file QR cũ
    const qrPath = path.join(__dirname, 'qr.png');
    if (fs.existsSync(qrPath)) {
      try {
        fs.unlinkSync(qrPath);
        console.log("Đã xóa file QR cũ để tạo mới");
      } catch (deleteError) {
        console.warn("Không thể xóa file QR cũ:", deleteError.message);
      }
    }

    // Reset trạng thái
    loginStatus = 'waiting';
    chatList = [];
    zaloApi = null;

    console.log("Đang tạo mã QR mới...");
    io.emit('status', { status: 'generating_qr', message: 'Đang tạo mã QR mới...' });

    // Khởi tạo Zalo instance mới và tạo QR
    const zalo = new Zalo();
    zaloApi = await zalo.loginQR();

    console.log("Đã tạo mã QR mới thành công!");
    io.emit('status', { status: 'waiting', message: 'Quét mã QR mới để đăng nhập' });

    res.json({ success: true, message: 'Đã tạo mã QR mới thành công' });

  } catch (error) {
    console.error("Lỗi khi làm mới QR:", error);
    loginStatus = 'error';
    io.emit('status', { status: 'error', message: 'Lỗi khi tạo QR mới: ' + error.message });
    res.status(500).json({ success: false, message: 'Lỗi khi làm mới QR: ' + error.message });
  }
});

app.post('/api/login', async (req, res) => {
  try {
    loginStatus = 'waiting';
    chatList = [];

    // Xóa file QR cũ nếu tồn tại
    const qrPath = path.join(__dirname, 'qr.png');
    if (fs.existsSync(qrPath)) {
      try {
        fs.unlinkSync(qrPath);
        console.log("Đã xóa file QR cũ");
      } catch (deleteError) {
        console.warn("Không thể xóa file QR cũ:", deleteError.message);
      }
    }

    // Khởi tạo Zalo instance
    const zalo = new Zalo();

    console.log("Đang tạo mã QR để đăng nhập...");
    io.emit('status', { status: 'generating_qr', message: 'Đang tạo mã QR...' });

    // Đăng nhập bằng QR
    zaloApi = await zalo.loginQR();

    console.log("Đăng nhập thành công!");
    loginStatus = 'success';
    
    // Emit success status
    io.emit('status', { status: 'success', message: 'Đăng nhập thành công!' });
    
    // Lấy danh sách chat
    try {
      const chats = await zaloApi.getContext();
      chatList = chats || [];

      io.emit('chats', { chats: chatList });
      console.log(`Đã tải ${chatList.length} đoạn chat`);
    } catch (chatError) {
      console.error("Lỗi khi lấy danh sách chat:", chatError);
      chatList = [];
    }
    
    res.json({ success: true, message: 'Đăng nhập thành công' });
    
  } catch (error) {
    console.error("Đã xảy ra lỗi:", error);
    loginStatus = 'error';
    io.emit('status', { status: 'error', message: 'Lỗi đăng nhập: ' + error.message });
    res.status(500).json({ success: false, message: error.message });
  }
});

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log('Client connected:', socket.id);
  
  // Gửi trạng thái hiện tại cho client mới
  socket.emit('status', { 
    status: loginStatus,
    hasQR: fs.existsSync(path.join(__dirname, 'qr.png'))
  });
  
  if (loginStatus === 'success' && chatList.length > 0) {
    socket.emit('chats', { chats: chatList });
  }
  
  socket.on('disconnect', () => {
    console.log('Client disconnected:', socket.id);
  });
  
  socket.on('refresh_chats', async () => {
    if (zaloApi && loginStatus === 'success') {
      try {
        const chats = await zaloApi.getContext();
        chatList = chats || [];
        io.emit('chats', { chats: chatList });
      } catch (error) {
        console.error("Lỗi khi refresh chat:", error);
        socket.emit('error', { message: 'Không thể tải lại danh sách chat' });
      }
    }
  });
});

const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
  console.log(`Server đang chạy tại http://localhost:${PORT}`);
});

// Socket.io connection
const socket = io();

// DOM elements
const loginBtn = document.getElementById('login-btn');
const refreshBtn = document.getElementById('refresh-btn');
const statusMessage = document.getElementById('status-message');
const qrContainer = document.getElementById('qr-container');
const qrImage = document.getElementById('qr-image');
const loadingSpinner = document.getElementById('loading-spinner');

// State
let isLoggingIn = false;

// Initialize
document.addEventListener('DOMContentLoaded', () => {
    checkStatus();
    setupEventListeners();
});

function setupEventListeners() {
    loginBtn.addEventListener('click', startLogin);
    refreshBtn.addEventListener('click', refreshQR);
    
    // Socket event listeners
    socket.on('status', handleStatusUpdate);
    socket.on('connect', () => {
        console.log('Connected to server');
    });
    
    socket.on('disconnect', () => {
        console.log('Disconnected from server');
        showStatus('Mất kết nối với server', 'error');
    });
}

async function checkStatus() {
    try {
        const response = await fetch('/api/status');
        const data = await response.json();
        
        if (data.status === 'success') {
            // Redirect to chats page if already logged in
            window.location.href = '/chats';
        } else if (data.hasQR) {
            showQRCode();
        }
    } catch (error) {
        console.error('Error checking status:', error);
        showStatus('Lỗi kết nối', 'error');
    }
}

async function startLogin() {
    if (isLoggingIn) return;
    
    isLoggingIn = true;
    loginBtn.disabled = true;
    showLoading(true);
    showStatus('Đang khởi tạo đăng nhập...', 'info');
    
    try {
        const response = await fetch('/api/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            showStatus('Đăng nhập thành công! Đang chuyển hướng...', 'success');
            setTimeout(() => {
                window.location.href = '/chats';
            }, 2000);
        } else {
            throw new Error(data.message || 'Đăng nhập thất bại');
        }
        
    } catch (error) {
        console.error('Login error:', error);
        showStatus('Lỗi đăng nhập: ' + error.message, 'error');
        isLoggingIn = false;
        loginBtn.disabled = false;
        showLoading(false);
    }
}

async function refreshQR() {
    if (isLoggingIn) return;

    refreshBtn.disabled = true;
    showLoading(true);
    showStatus('Đang làm mới mã QR...', 'info');

    try {
        const response = await fetch('/api/refresh-qr', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();

        if (data.success) {
            showStatus(data.message, 'success');
            // QR mới sẽ được hiển thị thông qua socket event 'waiting'
        } else {
            throw new Error(data.message || 'Không thể làm mới QR');
        }

    } catch (error) {
        console.error('Refresh QR error:', error);
        showStatus('Lỗi khi làm mới QR: ' + error.message, 'error');

        // Fallback: chỉ làm mới hình ảnh
        qrImage.src = '/qr.png?t=' + Date.now();
    } finally {
        refreshBtn.disabled = false;
        showLoading(false);
    }
}

function handleStatusUpdate(data) {
    console.log('Status update:', data);

    switch (data.status) {
        case 'generating_qr':
            showStatus(data.message, 'info');
            showLoading(true);
            break;

        case 'waiting':
            showQRCode();
            showStatus('Quét mã QR để đăng nhập', 'info');
            showLoading(false);
            break;



        case 'success':
            showStatus(data.message, 'success');
            showLoading(false);
            // Redirect ngay lập tức hoặc sau delay ngắn
            const redirectDelay = data.redirect ? 1000 : 2000;
            setTimeout(() => {
                window.location.href = data.redirect || '/chats';
            }, redirectDelay);
            break;

        case 'error':
            showStatus(data.message, 'error');
            showLoading(false);
            isLoggingIn = false;
            loginBtn.disabled = false;
            break;
    }
}

function showQRCode() {
    qrImage.src = '/qr.png?t=' + Date.now();
    qrContainer.style.display = 'block';
    refreshBtn.style.display = 'inline-block';
    loginBtn.style.display = 'none';
}

function showLoading(show) {
    loadingSpinner.style.display = show ? 'block' : 'none';
}

function showStatus(message, type = 'info') {
    statusMessage.textContent = message;
    statusMessage.className = `status-message ${type}`;
    statusMessage.style.display = 'block';
}

// Auto-refresh QR code every 2 minutes to prevent expiration
setInterval(() => {
    if (qrContainer.style.display === 'block' && !isLoggingIn) {
        refreshQR();
    }
}, 120000);

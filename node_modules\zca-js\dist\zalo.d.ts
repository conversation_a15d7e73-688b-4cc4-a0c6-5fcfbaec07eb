import { Listener } from "./apis/listen.js";
import { type ContextSession, type Options, type ZPWServiceMap } from "./context.js";
import toughCookie from "tough-cookie";
import { acceptFriendRequestFactory } from "./apis/acceptFriendRequest.js";
import { addGroupDeputyFactory } from "./apis/addGroupDeputy.js";
import { addReactionFactory } from "./apis/addReaction.js";
import { addUserToGroupFactory } from "./apis/addUserToGroup.js";
import { blockUserFactory } from "./apis/blockUser.js";
import { blockViewFeedFactory } from "./apis/blockViewFeed.js";
import { changeFriendAliasFactory } from "./apis/changeFriendAlias.js";
import { changeGroupAvatarFactory } from "./apis/changeGroupAvatar.js";
import { changeGroupNameFactory } from "./apis/changeGroupName.js";
import { changeGroupOwnerFactory } from "./apis/changeGroupOwner.js";
import { createGroupFactory } from "./apis/createGroup.js";
import { createNoteFactory } from "./apis/createNote.js";
import { createPollFactory } from "./apis/createPoll.js";
import { deleteMessageFactory } from "./apis/deleteMessage.js";
import { disperseGroupFactory } from "./apis/disperseGroup.js";
import { editNoteFactory } from "./apis/editNote.js";
import { fetchAccountInfoFactory } from "./apis/fetchAccountInfo.js";
import { findUserFactory } from "./apis/findUser.js";
import { getAliasListFactory } from "./apis/getAliasList.js";
import { getAllFriendsFactory } from "./apis/getAllFriends.js";
import { getAllGroupsFactory } from "./apis/getAllGroups.js";
import { getContextFactory } from "./apis/getContext.js";
import { getCookieFactory } from "./apis/getCookie.js";
import { getFriendRequestFactory } from "./apis/getFriendRequest.js";
import { getGroupInfoFactory } from "./apis/getGroupInfo.js";
import { getGroupMembersInfoFactory } from "./apis/getGroupMembersInfo.js";
import { getMuteFactory } from "./apis/getMute.js";
import { getLabelsFactory } from "./apis/getLabels.js";
import { getOwnIdFactory } from "./apis/getOwnId.js";
import { getPollDetailFactory } from "./apis/getPollDetail.js";
import { getQRFactory } from "./apis/getQR.js";
import { getStickersFactory } from "./apis/getStickers.js";
import { getStickersDetailFactory } from "./apis/getStickersDetail.js";
import { getUserInfoFactory } from "./apis/getUserInfo.js";
import { keepAliveFactory } from "./apis/keepAlive.js";
import { lockPollFactory } from "./apis/lockPoll.js";
import { type LoginQRCallback } from "./apis/loginQR.js";
import { parseLinkFactory } from "./apis/parseLink.js";
import { pinConversationsFactory } from "./apis/pinConversations.js";
import { removeGroupDeputyFactory } from "./apis/removeGroupDeputy.js";
import { removeUserFromGroupFactory } from "./apis/removeUserFromGroup.js";
import { sendCardFactory } from "./apis/sendCard.js";
import { sendDeliveredEventFactory } from "./apis/sendDeliveredEvent.js";
import { sendFriendRequestFactory } from "./apis/sendFriendRequest.js";
import { sendMessageFactory } from "./apis/sendMessage.js";
import { sendReportFactory } from "./apis/sendReport.js";
import { sendSeenEventFactory } from "./apis/sendSeenEvent.js";
import { sendStickerFactory } from "./apis/sendSticker.js";
import { sendTypingEventFactory } from "./apis/sendTypingEvent.js";
import { sendVideoFactory } from "./apis/sendVideo.js";
import { sendVoiceFactory } from "./apis/sendVoice.js";
import { unblockUserFactory } from "./apis/unblockUser.js";
import { undoFactory } from "./apis/undo.js";
import { updateLabelsFactory } from "./apis/updateLabels.js";
import { updateProfileFactory } from "./apis/updateProfile.js";
import { updateSettingsFactory } from "./apis/updateSettings.js";
import { uploadAttachmentFactory } from "./apis/uploadAttachment.js";
import { customFactory } from "./apis/custom.js";
export type Cookie = {
    domain: string;
    expirationDate: number;
    hostOnly: boolean;
    httpOnly: boolean;
    name: string;
    path: string;
    sameSite: string;
    secure: boolean;
    session: boolean;
    storeId: string;
    value: string;
};
export type Credentials = {
    imei: string;
    cookie: Cookie[] | toughCookie.SerializedCookie[] | {
        url: string;
        cookies: Cookie[];
    };
    userAgent: string;
    language?: string;
};
export declare class Zalo {
    private options;
    private enableEncryptParam;
    constructor(options?: Partial<Options>);
    private parseCookies;
    private validateParams;
    login(credentials: Credentials): Promise<API>;
    private loginCookie;
    loginQR(options?: {
        userAgent?: string;
        language?: string;
        qrPath?: string;
    }, callback?: LoginQRCallback): Promise<API>;
}
export declare class API {
    zpwServiceMap: ZPWServiceMap;
    listener: Listener;
    acceptFriendRequest: ReturnType<typeof acceptFriendRequestFactory>;
    addGroupDeputy: ReturnType<typeof addGroupDeputyFactory>;
    addReaction: ReturnType<typeof addReactionFactory>;
    addUserToGroup: ReturnType<typeof addUserToGroupFactory>;
    blockUser: ReturnType<typeof blockUserFactory>;
    blockViewFeed: ReturnType<typeof blockViewFeedFactory>;
    changeGroupAvatar: ReturnType<typeof changeGroupAvatarFactory>;
    changeGroupName: ReturnType<typeof changeGroupNameFactory>;
    changeGroupOwner: ReturnType<typeof changeGroupOwnerFactory>;
    changeFriendAlias: ReturnType<typeof changeFriendAliasFactory>;
    createGroup: ReturnType<typeof createGroupFactory>;
    createNote: ReturnType<typeof createNoteFactory>;
    createPoll: ReturnType<typeof createPollFactory>;
    deleteMessage: ReturnType<typeof deleteMessageFactory>;
    disperseGroup: ReturnType<typeof disperseGroupFactory>;
    editNote: ReturnType<typeof editNoteFactory>;
    fetchAccountInfo: ReturnType<typeof fetchAccountInfoFactory>;
    findUser: ReturnType<typeof findUserFactory>;
    getAliasList: ReturnType<typeof getAliasListFactory>;
    getAllFriends: ReturnType<typeof getAllFriendsFactory>;
    getAllGroups: ReturnType<typeof getAllGroupsFactory>;
    getCookie: ReturnType<typeof getCookieFactory>;
    getFriendRequest: ReturnType<typeof getFriendRequestFactory>;
    getGroupInfo: ReturnType<typeof getGroupInfoFactory>;
    getGroupMembersInfo: ReturnType<typeof getGroupMembersInfoFactory>;
    getMute: ReturnType<typeof getMuteFactory>;
    getLabels: ReturnType<typeof getLabelsFactory>;
    getOwnId: ReturnType<typeof getOwnIdFactory>;
    getPollDetail: ReturnType<typeof getPollDetailFactory>;
    getContext: ReturnType<typeof getContextFactory>;
    getQR: ReturnType<typeof getQRFactory>;
    getStickers: ReturnType<typeof getStickersFactory>;
    getStickersDetail: ReturnType<typeof getStickersDetailFactory>;
    getUserInfo: ReturnType<typeof getUserInfoFactory>;
    keepAlive: ReturnType<typeof keepAliveFactory>;
    lockPoll: ReturnType<typeof lockPollFactory>;
    parseLink: ReturnType<typeof parseLinkFactory>;
    pinConversations: ReturnType<typeof pinConversationsFactory>;
    removeGroupDeputy: ReturnType<typeof removeGroupDeputyFactory>;
    removeUserFromGroup: ReturnType<typeof removeUserFromGroupFactory>;
    sendCard: ReturnType<typeof sendCardFactory>;
    sendDeliveredEvent: ReturnType<typeof sendDeliveredEventFactory>;
    sendFriendRequest: ReturnType<typeof sendFriendRequestFactory>;
    sendMessage: ReturnType<typeof sendMessageFactory>;
    sendReport: ReturnType<typeof sendReportFactory>;
    sendSeenEvent: ReturnType<typeof sendSeenEventFactory>;
    sendSticker: ReturnType<typeof sendStickerFactory>;
    sendTypingEvent: ReturnType<typeof sendTypingEventFactory>;
    sendVideo: ReturnType<typeof sendVideoFactory>;
    sendVoice: ReturnType<typeof sendVoiceFactory>;
    unblockUser: ReturnType<typeof unblockUserFactory>;
    undo: ReturnType<typeof undoFactory>;
    updateLabels: ReturnType<typeof updateLabelsFactory>;
    updateProfile: ReturnType<typeof updateProfileFactory>;
    updateSettings: ReturnType<typeof updateSettingsFactory>;
    uploadAttachment: ReturnType<typeof uploadAttachmentFactory>;
    custom: ReturnType<typeof customFactory>;
    constructor(ctx: ContextSession, zpwServiceMap: ZPWServiceMap, wsUrls: string[]);
}

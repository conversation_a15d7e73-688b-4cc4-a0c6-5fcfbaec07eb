# Zalo Web Interface

Ứng dụng web để đăng nhập <PERSON><PERSON> và xem danh sách chat thông qua giao diện web hiện đại.

## Tính năng

- 🔐 Đăng nhập Zalo bằng QR code
- 💬 Hiển thị danh sách các đoạn chat
- 🔄 Tự động làm mới danh sách chat
- 📱 Giao diện responsive, thân thiện với mobile
- ⚡ Real-time updates với Socket.io

## Cài đặt

1. Cài đặt dependencies:

```bash
npm install
```

2. Khởi động server:

```bash
npm start
```

3. Truy cập ứng dụng tại: http://localhost:3000

## Cách sử dụng

### 1. Đăng nhập

- Truy cập http://localhost:3000
- Nhấn nút "Tạo mã QR"
- Quét mã QR bằng ứng dụng Zalo trên điện thoại
- Nếu QR hết hạn, nhấn "<PERSON>àm mới QR" để xóa QR cũ và tạo mã hoàn toàn mới
- Đợi đăng nhập thành công

### 2. Xem danh sách chat

- Sau khi đăng nhập thành công, bạn sẽ được chuyển đến trang danh sách chat
- Danh sách chat sẽ tự động tải và hiển thị
- Sử dụng nút "Làm mới" để cập nhật danh sách chat
- Nhấn "Đăng xuất" để quay lại trang đăng nhập

## Cấu trúc dự án

```
├── server.js              # Server Express chính
├── index.js               # File thông báo (đã tích hợp vào server)
├── index-standalone.js    # Phiên bản console gốc
├── package.json           # Dependencies và scripts
├── public/                # Static files
│   ├── login.html         # Trang đăng nhập
│   ├── chats.html         # Trang danh sách chat
│   ├── login.js           # JavaScript cho trang đăng nhập
│   ├── chats.js           # JavaScript cho trang chat
│   └── style.css          # CSS styling
└── qr.png                 # QR code (tự động tạo)
```

## API Endpoints

- `GET /` - Trang đăng nhập
- `GET /chats` - Trang danh sách chat
- `GET /qr.png` - Hình ảnh QR code
- `GET /api/status` - Trạng thái đăng nhập
- `GET /api/chats` - Danh sách chat (JSON)
- `POST /api/login` - Khởi tạo đăng nhập
- `POST /api/refresh-qr` - Làm mới mã QR (xóa file cũ)

## Socket.io Events

### Client → Server

- `refresh_chats` - Yêu cầu làm mới danh sách chat

### Server → Client

- `status` - Cập nhật trạng thái đăng nhập
- `chats` - Danh sách chat mới
- `error` - Thông báo lỗi

## Scripts

- `npm start` - Khởi động server production
- `npm run dev` - Khởi động server với nodemon (development)
- `node index-standalone.js` - Chạy phiên bản console gốc

## Lưu ý

- QR code sẽ tự động làm mới mỗi 2 phút để tránh hết hạn
- Nút "Làm mới QR" sẽ xóa file QR cũ và tạo QR hoàn toàn mới bằng `zaloApi = await zalo.loginQR()`
- Danh sách chat tự động cập nhật mỗi 30 giây
- Ứng dụng sử dụng thư viện `zca-js` để tương tác với Zalo
- Server chạy trên port 3000 (có thể thay đổi bằng biến môi trường PORT)

## Troubleshooting

### Lỗi kết nối

- Kiểm tra xem server có đang chạy không
- Đảm bảo port 3000 không bị chiếm dụng

### QR code không hiển thị

- Kiểm tra file qr.png có tồn tại không
- Thử làm mới trang hoặc nhấn "Làm mới"

### Không thể lấy danh sách chat

- Đảm bảo đã đăng nhập thành công
- Kiểm tra console để xem lỗi chi tiết
- Thử nhấn nút "Làm mới" trong trang chat

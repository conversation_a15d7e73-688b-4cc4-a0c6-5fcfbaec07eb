// Test script to demonstrate group processing
import { <PERSON>alo } from 'zca-js';

// Mock data for testing
const mockGroupIds = ['group1', 'group2', 'group3', 'group4', 'group5'];

// Configuration
const PROCESS_GROUPS_INDIVIDUALLY = true;

// Mock ChatEntityFactory
const ChatEntityFactory = {
  createFromGroup(group) {
    return {
      id: group.groupId || group.id,
      name: group.name || 'Unknown Group',
      type: 'group',
      avatar: group.avatar || '',
      memberCount: group.totalMember || 0,
      lastMessage: group.lastMessage || '',
      timestamp: group.timestamp || Date.now()
    };
  }
};

// Group processing utility (same as in server.js)
const groupUtils = {
  async processGroups(zaloApi, groupIds, logPrefix = '') {
    const groupEntities = [];
    
    if (PROCESS_GROUPS_INDIVIDUALLY) {
      console.log(`${logPrefix}Processing ${groupIds.length} groups individually...`);
      
      for (const groupId of groupIds) {
        try {
          console.log(`${logPrefix}Fetching info for group: ${groupId}`);
          
          // Mock API call - replace with actual zaloApi.getGroupInfo([groupId])
          const groupInfoResponse = await mockGetGroupInfo([groupId]);
          const groupInfo = Object.values(groupInfoResponse.gridInfoMap)[0];
          
          if (groupInfo) {
            const groupEntity = ChatEntityFactory.createFromGroup(groupInfo);
            groupEntities.push(groupEntity);
            console.log(`${logPrefix}✓ Successfully processed group: ${groupInfo.name || groupId}`);
          } else {
            console.warn(`${logPrefix}⚠ No info returned for group: ${groupId}`);
          }
        } catch (error) {
          console.warn(`${logPrefix}✗ Failed to get info for group ${groupId}:`, error.message);
        }
      }
      
      console.log(`${logPrefix}Completed processing groups. Total: ${groupEntities.length}/${groupIds.length}`);
    } else {
      // Process all groups at once (original method)
      console.log(`${logPrefix}Processing ${groupIds.length} groups in batch...`);
      try {
        const groupInfoResponse = await mockGetGroupInfo(groupIds);
        const groups = Object.values(groupInfoResponse.gridInfoMap);
        groupEntities.push(...groups.map(group => ChatEntityFactory.createFromGroup(group)));
        console.log(`${logPrefix}✓ Successfully processed ${groupEntities.length} groups in batch`);
      } catch (error) {
        console.warn(`${logPrefix}✗ Failed to process groups in batch:`, error.message);
      }
    }
    
    return groupEntities;
  }
};

// Mock API function for testing
async function mockGetGroupInfo(groupIds) {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 100));
  
  const gridInfoMap = {};
  
  for (const groupId of groupIds) {
    // Simulate some groups failing
    if (groupId === 'group3') {
      throw new Error(`API Error for group ${groupId}`);
    }
    
    gridInfoMap[groupId] = {
      groupId: groupId,
      name: `Test Group ${groupId}`,
      totalMember: Math.floor(Math.random() * 50) + 5,
      avatar: `https://example.com/avatar/${groupId}.jpg`,
      lastMessage: `Last message in ${groupId}`,
      timestamp: Date.now()
    };
  }
  
  return { gridInfoMap };
}

// Test function
async function testGroupProcessing() {
  console.log('=== Testing Group Processing ===\n');
  
  console.log('Configuration:');
  console.log(`PROCESS_GROUPS_INDIVIDUALLY: ${PROCESS_GROUPS_INDIVIDUALLY}\n`);
  
  try {
    const startTime = Date.now();
    const groupEntities = await groupUtils.processGroups(null, mockGroupIds, '[Test] ');
    const endTime = Date.now();
    
    console.log('\n=== Results ===');
    console.log(`Processing time: ${endTime - startTime}ms`);
    console.log(`Successfully processed: ${groupEntities.length}/${mockGroupIds.length} groups`);
    console.log('\nProcessed groups:');
    groupEntities.forEach(group => {
      console.log(`- ${group.name} (ID: ${group.id}, Members: ${group.memberCount})`);
    });
    
  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Run test
testGroupProcessing();

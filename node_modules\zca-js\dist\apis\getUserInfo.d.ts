export type ProfileInfo = {
    userId: string;
    username: string;
    displayName: string;
    zaloName: string;
    avatar: string;
    bgavatar: string;
    cover: string;
    gender: number;
    dob: number;
    sdob: string;
    status: string;
    phoneNumber: string;
    isFr: number;
    isBlocked: number;
    lastActionTime: number;
    lastUpdateTime: number;
    isActive: number;
    key: number;
    type: number;
    isActivePC: number;
    isActiveWeb: number;
    isValid: number;
    userKey: string;
    accountStatus: number;
    oaInfo: any;
    user_mode: number;
    globalId: string;
    bizPkg: {
        label: any;
        pkgId: number;
    };
    createdTs: number;
    oa_status: any;
};
export type UserInfoResponse = {
    unchanged_profiles: Record<string, any>;
    phonebook_version: number;
    changed_profiles: Record<string, ProfileInfo>;
};
export declare const getUserInfoFactory: (ctx: import("../context.js").ContextBase, api: import("../zalo.js").API) => (userId: string | string[]) => Promise<UserInfoResponse>;

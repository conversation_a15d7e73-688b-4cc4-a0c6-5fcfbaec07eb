// Socket.io connection
const socket = io();

// DOM elements
const refreshChatsBtn = document.getElementById('refresh-chats-btn');
const logoutBtn = document.getElementById('logout-btn');
const statusMessage = document.getElementById('status-message');
const chatList = document.getElementById('chat-list');
const loadingSpinner = document.getElementById('loading-spinner');
const emptyState = document.getElementById('empty-state');

// State
let chats = [];
let isLoading = false;

// Initialize
document.addEventListener('DOMContentLoaded', () => {
    checkLoginStatus();
    setupEventListeners();
    loadChats();
});

function setupEventListeners() {
    refreshChatsBtn.addEventListener('click', refreshChats);
    logoutBtn.addEventListener('click', logout);
    
    // Socket event listeners
    socket.on('chats', handleChatsUpdate);
    socket.on('status', handleStatusUpdate);
    socket.on('error', handleError);
    
    socket.on('connect', () => {
        console.log('Connected to server');
    });
    
    socket.on('disconnect', () => {
        console.log('Disconnected from server');
        showStatus('Mất kết nối với server', 'error');
    });
}

async function checkLoginStatus() {
    try {
        const response = await fetch('/api/status');
        const data = await response.json();
        
        if (data.status !== 'success') {
            // Redirect to login page if not logged in
            window.location.href = '/';
        }
    } catch (error) {
        console.error('Error checking login status:', error);
        window.location.href = '/';
    }
}

async function loadChats() {
    if (isLoading) return;
    
    isLoading = true;
    showLoading(true);
    showStatus('Đang tải danh sách chat...', 'info');
    
    try {
        const response = await fetch('/api/chats');
        const data = await response.json();
        
        chats = data.chats || [];
        renderChats();
        
        if (chats.length === 0) {
            showStatus('Không có đoạn chat nào', 'info');
        } else {
            hideStatus();
        }
        
    } catch (error) {
        console.error('Error loading chats:', error);
        showStatus('Lỗi khi tải danh sách chat', 'error');
    } finally {
        isLoading = false;
        showLoading(false);
    }
}

function refreshChats() {
    if (isLoading) return;
    
    refreshChatsBtn.disabled = true;
    socket.emit('refresh_chats');
    
    setTimeout(() => {
        refreshChatsBtn.disabled = false;
    }, 2000);
}

async function logout() {
    if (confirm('Bạn có chắc chắn muốn đăng xuất?')) {
        try {
            showLoading(true);
            showStatus('Đang đăng xuất...', 'info');

            const response = await fetch('/api/logout', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const data = await response.json();

            if (data.success) {
                showStatus('Đăng xuất thành công!', 'success');
                setTimeout(() => {
                    window.location.href = '/';
                }, 1000);
            } else {
                throw new Error(data.message || 'Đăng xuất thất bại');
            }

        } catch (error) {
            console.error('Logout error:', error);
            showStatus('Lỗi khi đăng xuất: ' + error.message, 'error');
            // Fallback: redirect anyway
            setTimeout(() => {
                window.location.href = '/';
            }, 2000);
        } finally {
            showLoading(false);
        }
    }
}

function handleChatsUpdate(data) {
    console.log('Chats update:', data);
    chats = data.chats || [];
    renderChats();
    
    if (chats.length === 0) {
        showStatus('Không có đoạn chat nào', 'info');
    } else {
        hideStatus();
    }
    
    showLoading(false);
}

function handleStatusUpdate(data) {
    console.log('Status update:', data);

    if (data.status === 'error') {
        showStatus(data.message, 'error');
    } else if (data.status === 'logged_out') {
        showStatus(data.message, 'info');
        setTimeout(() => {
            window.location.href = '/';
        }, 1000);
    }
}

function handleError(data) {
    console.error('Socket error:', data);
    showStatus(data.message || 'Đã xảy ra lỗi', 'error');
}

function renderChats() {
    chatList.innerHTML = '';
    
    if (chats.length === 0) {
        emptyState.style.display = 'block';
        return;
    }
    
    emptyState.style.display = 'none';
    
    chats.forEach(chat => {
        const chatItem = createChatItem(chat);
        chatList.appendChild(chatItem);
    });
}

function createChatItem(chat) {
    const chatItem = document.createElement('div');
    chatItem.className = 'chat-item';
    
    // Get first letter of chat name for avatar
    const avatarLetter = (chat.name || 'U').charAt(0).toUpperCase();
    
    chatItem.innerHTML = `
        <div class="chat-avatar">${avatarLetter}</div>
        <div class="chat-info">
            <div class="chat-name">${escapeHtml(chat.name || 'Không có tên')}</div>
            <div class="chat-id">ID: ${escapeHtml(chat.id || 'N/A')}</div>
        </div>
    `;
    
    // Add click event (for future functionality)
    chatItem.addEventListener('click', () => {
        console.log('Chat clicked:', chat);
        // TODO: Implement chat opening functionality
    });
    
    return chatItem;
}

function showLoading(show) {
    loadingSpinner.style.display = show ? 'block' : 'none';
}

function showStatus(message, type = 'info') {
    statusMessage.textContent = message;
    statusMessage.className = `status-message ${type}`;
    statusMessage.style.display = 'block';
}

function hideStatus() {
    statusMessage.style.display = 'none';
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Auto-refresh chats every 30 seconds
setInterval(() => {
    if (!isLoading) {
        socket.emit('refresh_chats');
    }
}, 30000);
